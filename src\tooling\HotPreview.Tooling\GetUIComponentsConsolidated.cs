using HotPreview.SharedModel;

namespace HotPreview.Tooling;

public class GetUIComponentsConsolidated : UIComponentsManagerBuilderTooling
{
    /// <summary>
    /// Initializes a new instance of GetUIComponentsConsolidated and consolidates the provided UI component managers,
    /// merging UI components with the same name and consolidating their previews.
    /// </summary>
    /// <param name="uiComponentsManagers">List of UI component managers to consolidate</param>
    public GetUIComponentsConsolidated(IList<UIComponentsManagerTooling> uiComponentsManagers)
    {
        foreach (UIComponentsManagerTooling manager in uiComponentsManagers)
        {
            // Merge categories
            foreach (UIComponentCategory category in manager.Categories)
            {
                AddCategory(category);
            }

            // Merge UI components
            foreach (UIComponentTooling uiComponent in manager.UIComponents)
            {
                if (_uiComponentsByName.TryGetValue(uiComponent.Name, out UIComponentTooling? existingComponent))
                {
                    // Component exists, merge previews
                    UIComponentTooling mergedComponent = MergeUIComponents(existingComponent, uiComponent);
                    _uiComponentsByName[uiComponent.Name] = mergedComponent;
                }
                else
                {
                    // Component doesn't exist, add it
                    _uiComponentsByName[uiComponent.Name] = uiComponent;
                }
            }
        }
    }

    /// <summary>
    /// Merges two UI components with the same name, consolidating their previews.
    /// Previews are considered the same if their Name is the same.
    /// </summary>
    /// <param name="existing">The existing UI component</param>
    /// <param name="incoming">The incoming UI component to merge</param>
    /// <returns>A new UI component with consolidated previews</returns>
    private static UIComponentTooling MergeUIComponents(UIComponentTooling existing, UIComponentTooling incoming)
    {
        // Use a dictionary to track previews by name to avoid duplicates
        var previewsByName = new Dictionary<string, PreviewTooling>();

        // Add existing previews
        foreach (PreviewTooling preview in existing.Previews)
        {
            previewsByName[preview.Name] = preview;
        }

        // Add incoming previews, keeping the original if there are duplicates
        foreach (PreviewTooling preview in incoming.Previews)
        {
            if (!previewsByName.TryGetValue(preview.Name, out PreviewTooling? existingPreview))
            {
                previewsByName[preview.Name] = preview;
            }
        }

        List<PreviewTooling> newPreviews = previewsByName.Values.ToList();

        // If there are a mix of auto-generated and user-defined previews, remove the auto-generated ones
        if (newPreviews.Any(e => e.IsAutoGenerated) && newPreviews.Any(e => !e.IsAutoGenerated))
        {
            newPreviews.RemoveAll(e => e.IsAutoGenerated);
        }

        // Create the merged component using the existing component's properties
        // but with the consolidated previews
        return new UIComponentTooling(
            existing.Kind,
            existing.Name,
            existing.DisplayNameOverride,
            newPreviews);
    }
}
