﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
    <PackageReference Include="StreamJsonRpc" Version="2.22.11" />
    <PackageReference Include="Magick.NET-Q8-AnyCPU" Version="13.5.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.3" />
    <PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.4" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\HotPreview\HotPreview.csproj" />
    <ProjectReference Include="..\..\HotPreview.SharedModel\HotPreview.SharedModel.csproj" />
  </ItemGroup>

</Project>
